{"name": "GooseDuckKill.Tests.Shared", "rootNamespace": "GooseDuckKill.Tests.Shared", "references": ["CustomNetworking", "GooseDuckKill.Core", "GooseDuckKill.Network", "GooseDuckKill.Player", "GooseDuckKill.UI"], "optionalUnityReferences": ["TestAssemblies"], "includePlatforms": [], "excludePlatforms": [], "allowUnsafeCode": false, "overrideReferences": false, "precompiledReferences": [], "autoReferenced": false, "defineConstraints": ["UNITY_INCLUDE_TESTS"], "versionDefines": [], "noEngineReferences": false}