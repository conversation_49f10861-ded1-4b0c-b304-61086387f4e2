{"name": "GooseDuckKill.Tests.EditMode", "rootNamespace": "GooseDuckKill.Tests", "references": ["GooseDuckKill.Tests.Shared", "CustomNetworking", "GooseDuckKill.Core", "GooseDuckKill.Network", "GooseDuckKill.Player", "GooseDuckKill.UI", "GooseDuckKill.Mobile"], "optionalUnityReferences": ["TestAssemblies"], "includePlatforms": ["Editor"], "excludePlatforms": [], "allowUnsafeCode": false, "overrideReferences": false, "precompiledReferences": [], "autoReferenced": false, "defineConstraints": ["UNITY_INCLUDE_TESTS"], "versionDefines": [], "noEngineReferences": false}