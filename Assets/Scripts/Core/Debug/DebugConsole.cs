using UnityEngine;
using System;
using System.Collections.Generic;
using System.Linq;
using CustomNetworking.Debug;

namespace GooseDuckKill.Core.Debug
{
    /// <summary>
    /// 统一调试控制台 - 核心调试控制器
    /// 提供平台自适应的调试功能和命令行接口
    /// </summary>
    public class DebugConsole : MonoBehaviour
    {
        [Header("调试设置")]
        [SerializeField] private bool enableDebugConsole = true;
        [SerializeField] private bool autoInitialize = true;
        [SerializeField] private bool persistAcrossScenes = true;

        [Header("平台设置")]
        [SerializeField] private bool enableMobileUI = true;
        [SerializeField] private bool enableDesktopUI = true;
        [SerializeField] private bool autoDetectPlatform = true;

        [Header("输入设置")]
        [SerializeField] private KeyCode toggleKey = KeyCode.F1;
        [SerializeField] private KeyCode commandKey = KeyCode.BackQuote; // ~ 键
        [SerializeField] private int maxTouchCount = 4; // 移动端激活手势
        [SerializeField] private float touchHoldTime = 2f;

        [Header("日志设置")]
        [SerializeField] private int maxLogEntries = 100;
        [SerializeField] private bool enableConsoleLogging = true;
        [SerializeField] private bool enableFileLogging = false;

        // 单例实例
        private static DebugConsole _instance;
        public static DebugConsole Instance
        {
            get
            {
                if (_instance == null)
                {
                    _instance = FindFirstObjectByType<DebugConsole>();
                    if (_instance == null)
                    {
                        GameObject go = new GameObject("DebugConsole");
                        _instance = go.AddComponent<DebugConsole>();
                        DontDestroyOnLoad(go);
                    }
                }
                return _instance;
            }
        }

        // 组件引用
        private NetworkDebugManager networkDebugManager;
        private GameObject desktopDebugUI;

        // 事件系统 - 用于与UI组件通信
        public static event System.Action<DebugLogEntry> OnLogEntryAdded;
        public static event System.Action<bool> OnMobileUIToggled;
        public static event System.Action<bool> OnDesktopUIToggled;
        public static event System.Action<string> OnCommandExecuted;

        // 状态管理
        private bool isInitialized = false;
        private bool isMobileUIVisible = false;
        private bool isDesktopUIVisible = false;
        private bool isCurrentPlatformMobile;

        // 命令系统
        private Dictionary<string, DebugCommand> registeredCommands;
        private List<string> commandHistory;
        private int commandHistoryIndex = -1;

        // 日志系统
        private List<DebugLogEntry> logEntries;
        private Queue<string> pendingLogs;

        // 触摸检测 (移动端)
        private float touchStartTime;
        private int currentTouchCount;

        #region Unity生命周期

        private void Awake()
        {
            // 单例模式
            if (_instance == null)
            {
                _instance = this;
                if (persistAcrossScenes)
                {
                    DontDestroyOnLoad(gameObject);
                }

                if (autoInitialize)
                {
                    InitializeDebugConsole();
                }
            }
            else if (_instance != this)
            {
                Destroy(gameObject);
            }
        }

        private void Start()
        {
            if (!isInitialized && autoInitialize)
            {
                InitializeDebugConsole();
            }
        }

        private void Update()
        {
            if (!enableDebugConsole || !isInitialized) return;

            HandleInput();
            ProcessPendingLogs();
        }

        private void OnDestroy()
        {
            if (_instance == this)
            {
                _instance = null;
            }
        }

        #endregion

        #region 初始化

        /// <summary>
        /// 初始化调试控制台
        /// </summary>
        public void InitializeDebugConsole()
        {
            if (isInitialized) return;

            // 检测平台
            DetectPlatform();

            // 初始化数据结构
            InitializeDataStructures();

            // 注册默认命令
            RegisterDefaultCommands();

            // 初始化UI组件
            InitializeUIComponents();

            // 设置日志监听
            SetupLogListening();

            isInitialized = true;

            LogDebug("DebugConsole initialized successfully", DebugLogType.Info);
        }

        /// <summary>
        /// 检测当前平台
        /// </summary>
        private void DetectPlatform()
        {
            if (autoDetectPlatform)
            {
                isCurrentPlatformMobile = Application.isMobilePlatform;
            }
            else
            {
                isCurrentPlatformMobile = enableMobileUI && !enableDesktopUI;
            }

            LogDebug($"Platform detected: {(isCurrentPlatformMobile ? "Mobile" : "Desktop")}", DebugLogType.Info);
        }

        /// <summary>
        /// 初始化数据结构
        /// </summary>
        private void InitializeDataStructures()
        {
            registeredCommands = new Dictionary<string, DebugCommand>();
            commandHistory = new List<string>();
            logEntries = new List<DebugLogEntry>();
            pendingLogs = new Queue<string>();
        }

        /// <summary>
        /// 初始化UI组件
        /// </summary>
        private void InitializeUIComponents()
        {
            // 获取或创建移动端UI
            if (isCurrentPlatformMobile && enableMobileUI)
            {
                InitializeMobileUI();
            }

            // 获取或创建桌面端UI
            if (!isCurrentPlatformMobile && enableDesktopUI)
            {
                InitializeDesktopUI();
            }

            // 获取网络调试管理器
            networkDebugManager = NetworkDebugManager.Instance;
        }

        /// <summary>
        /// 初始化移动端UI
        /// </summary>
        private void InitializeMobileUI()
        {
            // 通过事件通知UI组件初始化
            LogDebug("Mobile Debug UI initialization requested", DebugLogType.Info);
        }

        /// <summary>
        /// 初始化桌面端UI
        /// </summary>
        private void InitializeDesktopUI()
        {
            // 桌面端UI将在需要时动态创建
            LogDebug("Desktop Debug UI will be created when needed", DebugLogType.Info);
        }

        #endregion

        #region 输入处理

        /// <summary>
        /// 处理输入
        /// </summary>
        private void HandleInput()
        {
            // 桌面端输入
            if (!isCurrentPlatformMobile)
            {
                HandleDesktopInput();
            }
            // 移动端输入
            else
            {
                HandleMobileInput();
            }
        }

        /// <summary>
        /// 处理桌面端输入
        /// </summary>
        private void HandleDesktopInput()
        {
            // 切换调试UI
            if (Input.GetKeyDown(toggleKey))
            {
                ToggleDesktopUI();
            }

            // 命令行快捷键
            if (Input.GetKeyDown(commandKey))
            {
                ShowCommandInput();
            }
        }

        /// <summary>
        /// 处理移动端输入
        /// </summary>
        private void HandleMobileInput()
        {
            // 多点触摸激活
            if (Input.touchCount >= maxTouchCount)
            {
                if (currentTouchCount != Input.touchCount)
                {
                    currentTouchCount = Input.touchCount;
                    touchStartTime = Time.time;
                }
                else if (Time.time - touchStartTime >= touchHoldTime)
                {
                    ToggleMobileUI();
                    touchStartTime = float.MaxValue; // 防止重复触发
                }
            }
            else
            {
                currentTouchCount = 0;
            }
        }

        #endregion

        #region 公共接口

        /// <summary>
        /// 切换移动端UI显示
        /// </summary>
        public void ToggleMobileUI()
        {
            if (!enableMobileUI) return;

            isMobileUIVisible = !isMobileUIVisible;

            // 通过事件通知UI组件切换显示状态
            OnMobileUIToggled?.Invoke(isMobileUIVisible);

            LogDebug($"Mobile Debug UI {(isMobileUIVisible ? "shown" : "hidden")}", DebugLogType.Info);
        }

        /// <summary>
        /// 切换桌面端UI显示
        /// </summary>
        public void ToggleDesktopUI()
        {
            if (!enableDesktopUI) return;

            isDesktopUIVisible = !isDesktopUIVisible;

            // 通过事件通知UI组件切换显示状态
            OnDesktopUIToggled?.Invoke(isDesktopUIVisible);

            // 如果桌面UI不存在，创建它
            if (desktopDebugUI == null && isDesktopUIVisible)
            {
                CreateDesktopUI();
            }

            if (desktopDebugUI != null)
            {
                desktopDebugUI.SetActive(isDesktopUIVisible);
            }

            LogDebug($"Desktop Debug UI {(isDesktopUIVisible ? "shown" : "hidden")}", DebugLogType.Info);
        }

        /// <summary>
        /// 显示命令输入
        /// </summary>
        public void ShowCommandInput()
        {
            if (isCurrentPlatformMobile)
            {
                // 移动端显示虚拟键盘输入
                ShowMobileCommandInput();
            }
            else
            {
                // 桌面端显示命令行
                ShowDesktopCommandInput();
            }
        }

        /// <summary>
        /// 记录调试日志
        /// </summary>
        public void LogDebug(string message, DebugLogType type = DebugLogType.Info)
        {
            var entry = new DebugLogEntry
            {
                Message = message,
                Type = type,
                Timestamp = DateTime.Now
            };

            logEntries.Add(entry);

            // 限制日志数量
            if (logEntries.Count > maxLogEntries)
            {
                logEntries.RemoveAt(0);
            }

            // 控制台输出
            if (enableConsoleLogging)
            {
                string logMessage = $"[DebugConsole] {message}";
                switch (type)
                {
                    case DebugLogType.Error:
                        UnityEngine.Debug.LogError(logMessage);
                        break;
                    case DebugLogType.Warning:
                        UnityEngine.Debug.LogWarning(logMessage);
                        break;
                    case DebugLogType.Info:
                        UnityEngine.Debug.Log(logMessage);
                        break;
                }
            }

            // 通过事件通知UI更新
            OnLogEntryAdded?.Invoke(entry);
        }

        #endregion

        #region 命令系统

        /// <summary>
        /// 注册默认命令
        /// </summary>
        private void RegisterDefaultCommands()
        {
            // 基础命令
            RegisterCommand("help", "显示所有可用命令", ExecuteHelpCommand);
            RegisterCommand("clear", "清除日志", ExecuteClearCommand);
            RegisterCommand("quit", "退出应用程序", ExecuteQuitCommand);

            // 系统信息命令
            RegisterCommand("sysinfo", "显示系统信息", ExecuteSystemInfoCommand);
            RegisterCommand("fps", "显示FPS信息", ExecuteFPSCommand);
            RegisterCommand("memory", "显示内存信息", ExecuteMemoryCommand);

            // 调试命令
            RegisterCommand("toggle_mobile", "切换移动端UI", ExecuteToggleMobileCommand);
            RegisterCommand("toggle_desktop", "切换桌面端UI", ExecuteToggleDesktopCommand);
            RegisterCommand("log_level", "设置日志级别", ExecuteLogLevelCommand, 1, 1);

            // 网络命令
            RegisterCommand("net_info", "显示网络信息", ExecuteNetworkInfoCommand);
            RegisterCommand("net_debug", "切换网络调试", ExecuteNetworkDebugCommand);
        }

        /// <summary>
        /// 注册调试命令
        /// </summary>
        public void RegisterCommand(string name, string description, Action<string[]> action, int minArgs = 0, int maxArgs = -1)
        {
            var command = new DebugCommand(name, description, action, minArgs, maxArgs);
            registeredCommands[name.ToLower()] = command;
            LogDebug($"Command registered: {name}", DebugLogType.Info);
        }

        /// <summary>
        /// 执行命令
        /// </summary>
        public void ExecuteCommand(string commandLine)
        {
            if (string.IsNullOrWhiteSpace(commandLine)) return;

            // 添加到历史记录
            commandHistory.Add(commandLine);
            commandHistoryIndex = commandHistory.Count;

            // 解析命令
            string[] parts = commandLine.Split(' ', StringSplitOptions.RemoveEmptyEntries);
            if (parts.Length == 0) return;

            string commandName = parts[0].ToLower();
            string[] args = parts.Skip(1).ToArray();

            // 查找并执行命令
            if (registeredCommands.TryGetValue(commandName, out DebugCommand command))
            {
                // 检查参数数量
                if (args.Length < command.MinArgs)
                {
                    LogDebug($"Command '{commandName}' requires at least {command.MinArgs} arguments", DebugLogType.Error);
                    return;
                }

                if (command.MaxArgs >= 0 && args.Length > command.MaxArgs)
                {
                    LogDebug($"Command '{commandName}' accepts at most {command.MaxArgs} arguments", DebugLogType.Error);
                    return;
                }

                try
                {
                    command.Action?.Invoke(args);
                    // 通过事件通知命令执行
                    OnCommandExecuted?.Invoke(commandLine);
                }
                catch (Exception ex)
                {
                    LogDebug($"Error executing command '{commandName}': {ex.Message}", DebugLogType.Error);
                }
            }
            else
            {
                LogDebug($"Unknown command: {commandName}. Type 'help' for available commands.", DebugLogType.Warning);
            }
        }

        #endregion

        #region 默认命令实现

        private void ExecuteHelpCommand(string[] args)
        {
            LogDebug("=== Available Commands ===", DebugLogType.Info);
            foreach (var kvp in registeredCommands.OrderBy(x => x.Key))
            {
                var cmd = kvp.Value;
                string argInfo = "";
                if (cmd.MinArgs > 0 || cmd.MaxArgs >= 0)
                {
                    argInfo = $" (args: {cmd.MinArgs}-{(cmd.MaxArgs < 0 ? "∞" : cmd.MaxArgs.ToString())})";
                }
                LogDebug($"  {cmd.Name}{argInfo} - {cmd.Description}", DebugLogType.Info);
            }
        }

        private void ExecuteClearCommand(string[] args)
        {
            logEntries.Clear();
            LogDebug("Debug log cleared", DebugLogType.Info);
        }

        private void ExecuteQuitCommand(string[] args)
        {
            LogDebug("Quitting application...", DebugLogType.Info);
            #if UNITY_EDITOR
            UnityEditor.EditorApplication.isPlaying = false;
            #else
            Application.Quit();
            #endif
        }

        private void ExecuteSystemInfoCommand(string[] args)
        {
            LogDebug("=== System Information ===", DebugLogType.Info);
            LogDebug($"Platform: {Application.platform}", DebugLogType.Info);
            LogDebug($"Device: {SystemInfo.deviceModel}", DebugLogType.Info);
            LogDebug($"OS: {SystemInfo.operatingSystem}", DebugLogType.Info);
            LogDebug($"CPU: {SystemInfo.processorType} ({SystemInfo.processorCount} cores)", DebugLogType.Info);
            LogDebug($"GPU: {SystemInfo.graphicsDeviceName}", DebugLogType.Info);
            LogDebug($"Memory: {SystemInfo.systemMemorySize} MB", DebugLogType.Info);
            LogDebug($"Screen: {Screen.width}x{Screen.height} @ {Screen.currentResolution.refreshRate}Hz", DebugLogType.Info);
        }

        private void ExecuteFPSCommand(string[] args)
        {
            float fps = 1f / Time.deltaTime;
            LogDebug($"Current FPS: {fps:F1}", DebugLogType.Info);
            LogDebug($"Target FPS: {Application.targetFrameRate}", DebugLogType.Info);
            LogDebug($"Time Scale: {Time.timeScale}", DebugLogType.Info);
        }

        private void ExecuteMemoryCommand(string[] args)
        {
            long totalMemory = UnityEngine.Profiling.Profiler.GetTotalAllocatedMemory(false);
            long reservedMemory = UnityEngine.Profiling.Profiler.GetTotalReservedMemory(false);
            long unusedMemory = UnityEngine.Profiling.Profiler.GetTotalUnusedReservedMemory(false);

            LogDebug("=== Memory Information ===", DebugLogType.Info);
            LogDebug($"Total Allocated: {FormatBytes(totalMemory)}", DebugLogType.Info);
            LogDebug($"Total Reserved: {FormatBytes(reservedMemory)}", DebugLogType.Info);
            LogDebug($"Unused Reserved: {FormatBytes(unusedMemory)}", DebugLogType.Info);
            LogDebug($"System Memory: {SystemInfo.systemMemorySize} MB", DebugLogType.Info);
        }

        private void ExecuteToggleMobileCommand(string[] args)
        {
            ToggleMobileUI();
        }

        private void ExecuteToggleDesktopCommand(string[] args)
        {
            ToggleDesktopUI();
        }

        private void ExecuteLogLevelCommand(string[] args)
        {
            if (args.Length > 0)
            {
                LogDebug($"Log level command received: {args[0]}", DebugLogType.Info);
                // 这里可以实现日志级别设置逻辑
            }
        }

        private void ExecuteNetworkInfoCommand(string[] args)
        {
            if (networkDebugManager != null)
            {
                var stats = networkDebugManager.GetDebugStats();
                LogDebug("=== Network Information ===", DebugLogType.Info);
                LogDebug($"Connection State: {stats.ConnectionState}", DebugLogType.Info);
                LogDebug($"Ping: {stats.Ping:F0} ms", DebugLogType.Info);
                LogDebug($"Packet Loss: {stats.PacketLoss:F2}%", DebugLogType.Info);
                LogDebug($"Bandwidth In: {FormatBytes((long)stats.BandwidthIn)}/s", DebugLogType.Info);
                LogDebug($"Bandwidth Out: {FormatBytes((long)stats.BandwidthOut)}/s", DebugLogType.Info);
            }
            else
            {
                LogDebug("Network debug manager not available", DebugLogType.Warning);
            }
        }

        private void ExecuteNetworkDebugCommand(string[] args)
        {
            if (networkDebugManager != null)
            {
                networkDebugManager.ToggleDebugUI();
                LogDebug("Network debug UI toggled", DebugLogType.Info);
            }
            else
            {
                LogDebug("Network debug manager not available", DebugLogType.Warning);
            }
        }

        #endregion

        #region 工具方法

        /// <summary>
        /// 格式化字节数
        /// </summary>
        private string FormatBytes(long bytes)
        {
            string[] suffixes = { "B", "KB", "MB", "GB", "TB" };
            int suffixIndex = 0;
            double size = bytes;

            while (size >= 1024 && suffixIndex < suffixes.Length - 1)
            {
                size /= 1024;
                suffixIndex++;
            }

            return $"{size:F1} {suffixes[suffixIndex]}";
        }

        /// <summary>
        /// 设置日志监听
        /// </summary>
        private void SetupLogListening()
        {
            Application.logMessageReceived += OnLogMessageReceived;
        }

        /// <summary>
        /// Unity日志消息接收
        /// </summary>
        private void OnLogMessageReceived(string logString, string stackTrace, LogType type)
        {
            DebugLogType debugType = type switch
            {
                LogType.Error or LogType.Exception or LogType.Assert => DebugLogType.Error,
                LogType.Warning => DebugLogType.Warning,
                _ => DebugLogType.Info
            };

            // 避免递归日志
            if (!logString.StartsWith("[DebugConsole]"))
            {
                pendingLogs.Enqueue($"[Unity] {logString}");
            }
        }

        /// <summary>
        /// 处理待处理的日志
        /// </summary>
        private void ProcessPendingLogs()
        {
            while (pendingLogs.Count > 0)
            {
                string logMessage = pendingLogs.Dequeue();
                // 通过事件发送日志到UI
                var entry = new DebugLogEntry
                {
                    Message = logMessage,
                    Type = DebugLogType.Info,
                    Timestamp = DateTime.Now
                };
                OnLogEntryAdded?.Invoke(entry);
            }
        }



        /// <summary>
        /// 显示移动端命令输入
        /// </summary>
        private void ShowMobileCommandInput()
        {
            // TODO: 实现移动端命令输入界面
            LogDebug("Mobile command input not yet implemented", DebugLogType.Warning);
        }

        /// <summary>
        /// 显示桌面端命令输入
        /// </summary>
        private void ShowDesktopCommandInput()
        {
            // TODO: 实现桌面端命令输入界面
            LogDebug("Desktop command input not yet implemented", DebugLogType.Warning);
        }

        /// <summary>
        /// 创建桌面端UI
        /// </summary>
        private void CreateDesktopUI()
        {
            // TODO: 实现桌面端UI创建
            LogDebug("Desktop UI creation not yet implemented", DebugLogType.Warning);
        }

        #endregion
    }

    /// <summary>
    /// 调试命令结构
    /// </summary>
    [Serializable]
    public struct DebugCommand
    {
        public string Name;
        public string Description;
        public Action<string[]> Action;
        public int MinArgs;
        public int MaxArgs;

        public DebugCommand(string name, string description, Action<string[]> action, int minArgs = 0, int maxArgs = -1)
        {
            Name = name;
            Description = description;
            Action = action;
            MinArgs = minArgs;
            MaxArgs = maxArgs;
        }
    }

    /// <summary>
    /// 调试日志条目
    /// </summary>
    [Serializable]
    public struct DebugLogEntry
    {
        public string Message;
        public DebugLogType Type;
        public DateTime Timestamp;

        public string FormattedMessage => $"[{Timestamp:HH:mm:ss}] {Message}";
    }

    /// <summary>
    /// 调试日志类型
    /// </summary>
    public enum DebugLogType
    {
        Info,
        Warning,
        Error
    }
}
