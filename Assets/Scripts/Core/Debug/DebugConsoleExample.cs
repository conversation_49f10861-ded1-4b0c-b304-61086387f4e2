using UnityEngine;
using GooseDuckKill.Core.Debug;

namespace GooseDuckKill.Core.Debug
{
    /// <summary>
    /// DebugConsole 使用示例
    /// 展示如何使用统一调试控制台系统
    /// </summary>
    public class DebugConsoleExample : MonoBehaviour
    {
        [Header("示例设置")]
        [SerializeField] private bool autoRegisterCommands = true;
        [SerializeField] private bool showUsageInstructions = true;
        
        private void Start()
        {
            if (autoRegisterCommands)
            {
                RegisterExampleCommands();
            }
            
            if (showUsageInstructions)
            {
                ShowUsageInstructions();
            }
        }
        
        /// <summary>
        /// 注册示例命令
        /// </summary>
        private void RegisterExampleCommands()
        {
            var debugConsole = DebugConsole.Instance;
            
            // 注册游戏相关命令
            debugConsole.RegisterCommand("spawn_player", "生成玩家", ExecuteSpawnPlayerCommand, 0, 1);
            debugConsole.RegisterCommand("set_health", "设置生命值", ExecuteSetHealthCommand, 1, 1);
            debugConsole.RegisterCommand("teleport", "传送玩家", ExecuteTeleportCommand, 2, 2);
            debugConsole.RegisterCommand("give_item", "给予物品", ExecuteGiveItemCommand, 1, 2);
            
            // 注册场景相关命令
            debugConsole.RegisterCommand("load_scene", "加载场景", ExecuteLoadSceneCommand, 1, 1);
            debugConsole.RegisterCommand("reload_scene", "重新加载当前场景", ExecuteReloadSceneCommand);
            debugConsole.RegisterCommand("list_scenes", "列出所有场景", ExecuteListScenesCommand);
            
            // 注册测试命令
            debugConsole.RegisterCommand("test_log", "测试日志输出", ExecuteTestLogCommand, 0, 1);
            debugConsole.RegisterCommand("test_error", "测试错误输出", ExecuteTestErrorCommand);
            debugConsole.RegisterCommand("test_performance", "测试性能", ExecuteTestPerformanceCommand);
            
            debugConsole.LogDebug("Example commands registered successfully", DebugLogType.Info);
        }
        
        /// <summary>
        /// 显示使用说明
        /// </summary>
        private void ShowUsageInstructions()
        {
            var debugConsole = DebugConsole.Instance;
            
            debugConsole.LogDebug("=== DebugConsole 使用说明 ===", DebugLogType.Info);
            debugConsole.LogDebug("桌面端:", DebugLogType.Info);
            debugConsole.LogDebug("  - 按 F1 切换调试UI", DebugLogType.Info);
            debugConsole.LogDebug("  - 按 ~ 键打开命令行", DebugLogType.Info);
            debugConsole.LogDebug("移动端:", DebugLogType.Info);
            debugConsole.LogDebug("  - 四指长按2秒激活调试UI", DebugLogType.Info);
            debugConsole.LogDebug("常用命令:", DebugLogType.Info);
            debugConsole.LogDebug("  - help: 显示所有命令", DebugLogType.Info);
            debugConsole.LogDebug("  - clear: 清除日志", DebugLogType.Info);
            debugConsole.LogDebug("  - sysinfo: 显示系统信息", DebugLogType.Info);
            debugConsole.LogDebug("  - fps: 显示FPS信息", DebugLogType.Info);
            debugConsole.LogDebug("  - memory: 显示内存信息", DebugLogType.Info);
            debugConsole.LogDebug("  - net_info: 显示网络信息", DebugLogType.Info);
        }
        
        #region 示例命令实现
        
        private void ExecuteSpawnPlayerCommand(string[] args)
        {
            string playerName = args.Length > 0 ? args[0] : "Player";
            DebugConsole.Instance.LogDebug($"Spawning player: {playerName}", DebugLogType.Info);
            // 这里实现生成玩家的逻辑
        }
        
        private void ExecuteSetHealthCommand(string[] args)
        {
            if (float.TryParse(args[0], out float health))
            {
                DebugConsole.Instance.LogDebug($"Setting player health to: {health}", DebugLogType.Info);
                // 这里实现设置生命值的逻辑
            }
            else
            {
                DebugConsole.Instance.LogDebug("Invalid health value", DebugLogType.Error);
            }
        }
        
        private void ExecuteTeleportCommand(string[] args)
        {
            if (float.TryParse(args[0], out float x) && float.TryParse(args[1], out float y))
            {
                DebugConsole.Instance.LogDebug($"Teleporting player to: ({x}, {y})", DebugLogType.Info);
                // 这里实现传送玩家的逻辑
            }
            else
            {
                DebugConsole.Instance.LogDebug("Invalid coordinates", DebugLogType.Error);
            }
        }
        
        private void ExecuteGiveItemCommand(string[] args)
        {
            string itemName = args[0];
            int quantity = args.Length > 1 && int.TryParse(args[1], out int q) ? q : 1;
            DebugConsole.Instance.LogDebug($"Giving {quantity}x {itemName} to player", DebugLogType.Info);
            // 这里实现给予物品的逻辑
        }
        
        private void ExecuteLoadSceneCommand(string[] args)
        {
            string sceneName = args[0];
            DebugConsole.Instance.LogDebug($"Loading scene: {sceneName}", DebugLogType.Info);
            // 这里实现加载场景的逻辑
            // UnityEngine.SceneManagement.SceneManager.LoadScene(sceneName);
        }
        
        private void ExecuteReloadSceneCommand(string[] args)
        {
            string currentScene = UnityEngine.SceneManagement.SceneManager.GetActiveScene().name;
            DebugConsole.Instance.LogDebug($"Reloading current scene: {currentScene}", DebugLogType.Info);
            // UnityEngine.SceneManagement.SceneManager.LoadScene(currentScene);
        }
        
        private void ExecuteListScenesCommand(string[] args)
        {
            DebugConsole.Instance.LogDebug("=== Available Scenes ===", DebugLogType.Info);
            for (int i = 0; i < UnityEngine.SceneManagement.SceneManager.sceneCountInBuildSettings; i++)
            {
                string scenePath = UnityEngine.SceneManagement.SceneUtility.GetScenePathByBuildIndex(i);
                string sceneName = System.IO.Path.GetFileNameWithoutExtension(scenePath);
                DebugConsole.Instance.LogDebug($"  {i}: {sceneName}", DebugLogType.Info);
            }
        }
        
        private void ExecuteTestLogCommand(string[] args)
        {
            string message = args.Length > 0 ? args[0] : "Test log message";
            DebugConsole.Instance.LogDebug($"Test Log: {message}", DebugLogType.Info);
            DebugConsole.Instance.LogDebug($"Test Warning: {message}", DebugLogType.Warning);
            DebugConsole.Instance.LogDebug($"Test Error: {message}", DebugLogType.Error);
        }
        
        private void ExecuteTestErrorCommand(string[] args)
        {
            DebugConsole.Instance.LogDebug("This is a test error message", DebugLogType.Error);
            // 也可以触发Unity的错误日志
            Debug.LogError("This is a Unity error log for testing");
        }
        
        private void ExecuteTestPerformanceCommand(string[] args)
        {
            DebugConsole.Instance.LogDebug("=== Performance Test ===", DebugLogType.Info);
            
            // 测试FPS
            float fps = 1f / Time.deltaTime;
            DebugConsole.Instance.LogDebug($"Current FPS: {fps:F1}", DebugLogType.Info);
            
            // 测试内存
            long totalMemory = UnityEngine.Profiling.Profiler.GetTotalAllocatedMemory(false);
            DebugConsole.Instance.LogDebug($"Total Memory: {FormatBytes(totalMemory)}", DebugLogType.Info);
            
            // 测试系统信息
            DebugConsole.Instance.LogDebug($"Platform: {Application.platform}", DebugLogType.Info);
            DebugConsole.Instance.LogDebug($"Device: {SystemInfo.deviceModel}", DebugLogType.Info);
        }
        
        #endregion
        
        #region 工具方法
        
        /// <summary>
        /// 格式化字节数
        /// </summary>
        private string FormatBytes(long bytes)
        {
            string[] suffixes = { "B", "KB", "MB", "GB", "TB" };
            int suffixIndex = 0;
            double size = bytes;
            
            while (size >= 1024 && suffixIndex < suffixes.Length - 1)
            {
                size /= 1024;
                suffixIndex++;
            }
            
            return $"{size:F1} {suffixes[suffixIndex]}";
        }
        
        #endregion
        
        #region 测试方法 (仅在开发模式下)
        
        #if UNITY_EDITOR || DEVELOPMENT_BUILD
        
        [ContextMenu("Test Debug Console")]
        private void TestDebugConsole()
        {
            var debugConsole = DebugConsole.Instance;
            debugConsole.LogDebug("Testing DebugConsole from context menu", DebugLogType.Info);
            debugConsole.ExecuteCommand("help");
        }
        
        [ContextMenu("Toggle Mobile UI")]
        private void TestToggleMobileUI()
        {
            DebugConsole.Instance.ToggleMobileUI();
        }
        
        [ContextMenu("Toggle Desktop UI")]
        private void TestToggleDesktopUI()
        {
            DebugConsole.Instance.ToggleDesktopUI();
        }
        
        [ContextMenu("Execute Test Commands")]
        private void TestExecuteCommands()
        {
            var debugConsole = DebugConsole.Instance;
            debugConsole.ExecuteCommand("sysinfo");
            debugConsole.ExecuteCommand("fps");
            debugConsole.ExecuteCommand("memory");
            debugConsole.ExecuteCommand("test_log Hello World");
        }
        
        #endif
        
        #endregion
    }
}
