using UnityEngine;
using GooseDuckKill.Core.Debug;

namespace GooseDuckKill.Core.Debug
{
    /// <summary>
    /// DebugConsole 编译测试
    /// 验证循环依赖是否已解决
    /// </summary>
    public class DebugConsoleCompilationTest : MonoBehaviour
    {
        [Header("编译测试")]
        [SerializeField] private bool runTestOnStart = false;
        
        private void Start()
        {
            if (runTestOnStart)
            {
                RunCompilationTest();
            }
        }
        
        /// <summary>
        /// 运行编译测试
        /// </summary>
        [ContextMenu("Run Compilation Test")]
        public void RunCompilationTest()
        {
            Debug.Log("=== DebugConsole Compilation Test ===");
            
            try
            {
                // 测试DebugConsole实例化
                var debugConsole = DebugConsole.Instance;
                Debug.Log("✓ DebugConsole.Instance - OK");
                
                // 测试日志功能
                debugConsole.LogDebug("Test log message", DebugLogType.Info);
                Debug.Log("✓ LogDebug - OK");
                
                // 测试命令注册
                debugConsole.RegisterCommand("test_cmd", "Test command", TestCommandHandler);
                Debug.Log("✓ RegisterCommand - OK");
                
                // 测试命令执行
                debugConsole.ExecuteCommand("help");
                Debug.Log("✓ ExecuteCommand - OK");
                
                // 测试UI切换
                debugConsole.ToggleMobileUI();
                debugConsole.ToggleDesktopUI();
                Debug.Log("✓ UI Toggle methods - OK");
                
                Debug.Log("=== All Tests Passed! ===");
                Debug.Log("DebugConsole is working correctly without circular dependencies.");
                
            }
            catch (System.Exception ex)
            {
                Debug.LogError($"❌ Compilation Test Failed: {ex.Message}");
                Debug.LogError($"Stack Trace: {ex.StackTrace}");
            }
        }
        
        /// <summary>
        /// 测试命令处理器
        /// </summary>
        private void TestCommandHandler(string[] args)
        {
            Debug.Log("Test command executed successfully!");
        }
        
        /// <summary>
        /// 测试事件系统
        /// </summary>
        [ContextMenu("Test Event System")]
        public void TestEventSystem()
        {
            Debug.Log("=== Testing Event System ===");
            
            // 订阅事件
            DebugConsole.OnLogEntryAdded += OnTestLogEntryAdded;
            DebugConsole.OnMobileUIToggled += OnTestMobileUIToggled;
            DebugConsole.OnCommandExecuted += OnTestCommandExecuted;
            
            Debug.Log("✓ Event subscription - OK");
            
            // 触发事件
            var debugConsole = DebugConsole.Instance;
            debugConsole.LogDebug("Test event log", DebugLogType.Info);
            debugConsole.ToggleMobileUI();
            debugConsole.ExecuteCommand("test_cmd");
            
            // 取消订阅
            DebugConsole.OnLogEntryAdded -= OnTestLogEntryAdded;
            DebugConsole.OnMobileUIToggled -= OnTestMobileUIToggled;
            DebugConsole.OnCommandExecuted -= OnTestCommandExecuted;
            
            Debug.Log("✓ Event unsubscription - OK");
            Debug.Log("=== Event System Test Completed ===");
        }
        
        private void OnTestLogEntryAdded(DebugLogEntry entry)
        {
            Debug.Log($"✓ Event received - LogEntryAdded: {entry.Message}");
        }
        
        private void OnTestMobileUIToggled(bool isVisible)
        {
            Debug.Log($"✓ Event received - MobileUIToggled: {isVisible}");
        }
        
        private void OnTestCommandExecuted(string command)
        {
            Debug.Log($"✓ Event received - CommandExecuted: {command}");
        }
        
        /// <summary>
        /// 测试程序集依赖
        /// </summary>
        [ContextMenu("Test Assembly Dependencies")]
        public void TestAssemblyDependencies()
        {
            Debug.Log("=== Testing Assembly Dependencies ===");
            
            // 测试Core程序集类型
            var debugConsoleType = typeof(DebugConsole);
            Debug.Log($"✓ DebugConsole type: {debugConsoleType.FullName}");
            Debug.Log($"✓ Assembly: {debugConsoleType.Assembly.GetName().Name}");
            
            // 测试事件类型
            var logEntryType = typeof(DebugLogEntry);
            Debug.Log($"✓ DebugLogEntry type: {logEntryType.FullName}");
            
            var commandType = typeof(DebugCommand);
            Debug.Log($"✓ DebugCommand type: {commandType.FullName}");
            
            Debug.Log("=== Assembly Dependencies Test Completed ===");
            Debug.Log("No circular dependencies detected!");
        }
    }
}
