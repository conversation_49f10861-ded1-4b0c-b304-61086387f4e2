using UnityEngine;
using System.Reflection;
using System.Collections.Generic;
using System.Linq;

namespace GooseDuckKill.Core.Debug
{
    /// <summary>
    /// 程序集依赖测试
    /// 验证程序集依赖关系是否正确，无循环依赖
    /// </summary>
    public class AssemblyDependencyTest : MonoBehaviour
    {
        [Header("测试设置")]
        [SerializeField] private bool runTestOnStart = false;
        [SerializeField] private bool logDetailedInfo = true;
        
        private void Start()
        {
            if (runTestOnStart)
            {
                TestAssemblyDependencies();
            }
        }
        
        /// <summary>
        /// 测试程序集依赖关系
        /// </summary>
        [ContextMenu("Test Assembly Dependencies")]
        public void TestAssemblyDependencies()
        {
            Debug.Log("=== Assembly Dependency Test ===");
            
            try
            {
                // 获取所有相关程序集
                var assemblies = GetProjectAssemblies();
                
                Debug.Log($"Found {assemblies.Count} project assemblies:");
                foreach (var assembly in assemblies)
                {
                    Debug.Log($"  - {assembly.GetName().Name}");
                }
                
                // 检查依赖关系
                CheckDependencies(assemblies);
                
                // 验证特定类型
                VerifySpecificTypes();
                
                Debug.Log("=== Assembly Dependency Test Completed Successfully ===");
                
            }
            catch (System.Exception ex)
            {
                Debug.LogError($"❌ Assembly Dependency Test Failed: {ex.Message}");
                Debug.LogError($"Stack Trace: {ex.StackTrace}");
            }
        }
        
        /// <summary>
        /// 获取项目相关程序集
        /// </summary>
        private List<Assembly> GetProjectAssemblies()
        {
            var projectAssemblies = new List<Assembly>();
            var allAssemblies = System.AppDomain.CurrentDomain.GetAssemblies();
            
            foreach (var assembly in allAssemblies)
            {
                string name = assembly.GetName().Name;
                if (name.StartsWith("GooseDuckKill") || name == "CustomNetworking")
                {
                    projectAssemblies.Add(assembly);
                }
            }
            
            return projectAssemblies;
        }
        
        /// <summary>
        /// 检查程序集依赖关系
        /// </summary>
        private void CheckDependencies(List<Assembly> assemblies)
        {
            Debug.Log("--- Checking Assembly Dependencies ---");
            
            foreach (var assembly in assemblies)
            {
                string assemblyName = assembly.GetName().Name;
                var referencedAssemblies = assembly.GetReferencedAssemblies();
                
                if (logDetailedInfo)
                {
                    Debug.Log($"Assembly: {assemblyName}");
                    Debug.Log($"  References: {referencedAssemblies.Length} assemblies");
                    
                    foreach (var refAssembly in referencedAssemblies)
                    {
                        if (refAssembly.Name.StartsWith("GooseDuckKill") || refAssembly.Name == "CustomNetworking")
                        {
                            Debug.Log($"    -> {refAssembly.Name}");
                        }
                    }
                }
                
                // 检查是否有循环依赖
                CheckCircularDependency(assembly, assemblies);
            }
        }
        
        /// <summary>
        /// 检查循环依赖
        /// </summary>
        private void CheckCircularDependency(Assembly assembly, List<Assembly> allAssemblies)
        {
            var visited = new HashSet<string>();
            var recursionStack = new HashSet<string>();
            
            if (HasCircularDependency(assembly.GetName().Name, allAssemblies, visited, recursionStack))
            {
                Debug.LogError($"❌ Circular dependency detected involving: {assembly.GetName().Name}");
            }
            else if (logDetailedInfo)
            {
                Debug.Log($"✓ No circular dependency for: {assembly.GetName().Name}");
            }
        }
        
        /// <summary>
        /// 递归检查循环依赖
        /// </summary>
        private bool HasCircularDependency(string assemblyName, List<Assembly> allAssemblies, 
            HashSet<string> visited, HashSet<string> recursionStack)
        {
            if (recursionStack.Contains(assemblyName))
            {
                return true; // 发现循环
            }
            
            if (visited.Contains(assemblyName))
            {
                return false; // 已经检查过
            }
            
            visited.Add(assemblyName);
            recursionStack.Add(assemblyName);
            
            // 查找对应的程序集
            var assembly = allAssemblies.FirstOrDefault(a => a.GetName().Name == assemblyName);
            if (assembly != null)
            {
                var referencedAssemblies = assembly.GetReferencedAssemblies();
                foreach (var refAssembly in referencedAssemblies)
                {
                    if (refAssembly.Name.StartsWith("GooseDuckKill") || refAssembly.Name == "CustomNetworking")
                    {
                        if (HasCircularDependency(refAssembly.Name, allAssemblies, visited, recursionStack))
                        {
                            return true;
                        }
                    }
                }
            }
            
            recursionStack.Remove(assemblyName);
            return false;
        }
        
        /// <summary>
        /// 验证特定类型
        /// </summary>
        private void VerifySpecificTypes()
        {
            Debug.Log("--- Verifying Specific Types ---");
            
            // 验证DebugConsole类型
            var debugConsoleType = typeof(DebugConsole);
            Debug.Log($"✓ DebugConsole type found: {debugConsoleType.FullName}");
            Debug.Log($"  Assembly: {debugConsoleType.Assembly.GetName().Name}");
            
            // 验证事件类型
            var logEntryType = typeof(DebugLogEntry);
            Debug.Log($"✓ DebugLogEntry type found: {logEntryType.FullName}");
            
            // 验证NetworkDebugManager类型
            try
            {
                var networkDebugType = typeof(CustomNetworking.Debug.NetworkDebugManager);
                Debug.Log($"✓ NetworkDebugManager type found: {networkDebugType.FullName}");
                Debug.Log($"  Assembly: {networkDebugType.Assembly.GetName().Name}");
            }
            catch (System.Exception ex)
            {
                Debug.LogWarning($"⚠️ NetworkDebugManager type not accessible: {ex.Message}");
            }
            
            // 验证Core程序集类型
            try
            {
                var gameManagerType = typeof(GooseDuckKill.Core.GameManager);
                Debug.Log($"✓ GameManager type found: {gameManagerType.FullName}");
                Debug.Log($"  Assembly: {gameManagerType.Assembly.GetName().Name}");
            }
            catch (System.Exception ex)
            {
                Debug.LogWarning($"⚠️ GameManager type not accessible: {ex.Message}");
            }
        }
        
        /// <summary>
        /// 生成依赖关系图
        /// </summary>
        [ContextMenu("Generate Dependency Graph")]
        public void GenerateDependencyGraph()
        {
            Debug.Log("=== Assembly Dependency Graph ===");
            
            var assemblies = GetProjectAssemblies();
            
            foreach (var assembly in assemblies)
            {
                string assemblyName = assembly.GetName().Name;
                var referencedAssemblies = assembly.GetReferencedAssemblies();
                
                Debug.Log($"{assemblyName}:");
                
                var projectReferences = referencedAssemblies
                    .Where(r => r.Name.StartsWith("GooseDuckKill") || r.Name == "CustomNetworking")
                    .ToList();
                
                if (projectReferences.Count > 0)
                {
                    foreach (var refAssembly in projectReferences)
                    {
                        Debug.Log($"  └── {refAssembly.Name}");
                    }
                }
                else
                {
                    Debug.Log($"  └── (no project dependencies)");
                }
            }
        }
        
        /// <summary>
        /// 测试DebugConsole功能
        /// </summary>
        [ContextMenu("Test DebugConsole Functionality")]
        public void TestDebugConsoleFunctionality()
        {
            Debug.Log("=== Testing DebugConsole Functionality ===");
            
            try
            {
                // 测试DebugConsole实例化
                var debugConsole = DebugConsole.Instance;
                Debug.Log("✓ DebugConsole.Instance created successfully");
                
                // 测试事件系统
                bool eventReceived = false;
                DebugConsole.OnLogEntryAdded += (entry) => {
                    eventReceived = true;
                    Debug.Log($"✓ Event received: {entry.Message}");
                };
                
                // 触发事件
                debugConsole.LogDebug("Test event message", DebugLogType.Info);
                
                if (eventReceived)
                {
                    Debug.Log("✓ Event system working correctly");
                }
                else
                {
                    Debug.LogWarning("⚠️ Event system may not be working");
                }
                
                Debug.Log("=== DebugConsole Functionality Test Completed ===");
                
            }
            catch (System.Exception ex)
            {
                Debug.LogError($"❌ DebugConsole Functionality Test Failed: {ex.Message}");
            }
        }
    }
}
