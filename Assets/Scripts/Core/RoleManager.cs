using System;
using System.Collections.Generic;
using UnityEngine;
using CustomNetworking.Core;

// 使用别名避免命名空间冲突
using UDebug = UnityEngine.Debug;

namespace GooseDuckKill.Core
{
    /// <summary>
    /// 玩家控制器接口
    /// </summary>
    public interface IPlayerController
    {
        void SetAsImpostor(bool isImpostor);
    }

    /// <summary>
    /// 角色管理器 - 负责角色分配和管理
    /// </summary>
    public class RoleManager : NetworkBehaviour
    {
        /// <summary>
        /// 角色类型枚举
        /// </summary>
        public enum RoleType
        {
            Goose,      // 鹅（好人）
            Duck,       // 鸭子（杀手）
            Neutral     // 中立角色
        }

        /// <summary>
        /// 角色配置类
        /// </summary>
        [Serializable]
        public class RoleConfig
        {
            public string roleName;
            public RoleType roleType;
            public int maxCount;
            public bool enabledInGame = true;
        }

        /// <summary>
        /// 玩家角色数据
        /// </summary>
        [Serializable]
        private class PlayerRoleData
        {
            public RoleType roleType;
            public Dictionary<string, float> abilityCooldowns = new Dictionary<string, float>();
            public bool isAlive = true;

            public PlayerRoleData(RoleType type)
            {
                roleType = type;
                isAlive = true;
            }
        }

        // 角色配置
        [Header("角色配置")]
        [SerializeField] private List<RoleConfig> roleConfigs = new List<RoleConfig>();

        [Header("角色比例")]
        [SerializeField, Range(0.1f, 0.5f)] private float duckRatio = 0.2f; // 鸭子玩家比例
        [SerializeField, Range(0f, 0.2f)] private float neutralRatio = 0.1f; // 中立玩家比例
        [SerializeField] private int minDucks = 1; // 最少鸭子数量
        [SerializeField] private int maxDucks = 3; // 最多鸭子数量

        // 角色事件
        public event Action<PlayerRef, RoleType> OnRoleAssigned;
        public event Action<PlayerRef> OnPlayerDied;

        // 玩家角色数据字典
        private Dictionary<PlayerRef, PlayerRoleData> _playerRoleData = new Dictionary<PlayerRef, PlayerRoleData>();

        // 能力冷却配置
        private Dictionary<string, float> _abilityCooldowns = new Dictionary<string, float>
        {
            { "kill", 30f },   // 杀人能力冷却
            { "vent", 15f },   // 通风口能力冷却
            { "sabotage", 45f } // 破坏能力冷却
        };

        /// <summary>
        /// 初始化
        /// </summary>
        public override void Spawned()
        {
            base.Spawned();

            UDebug.Log("RoleManager 初始化完成");
        }

        #region 角色分配

        /// <summary>
        /// 分配所有玩家角色
        /// </summary>
        public void AssignRoles()
        {
            if (!Runner.IsServer) return;

            // 清空角色数据
            _playerRoleData.Clear();

            var players = GetAllPlayers();
            int playerCount = players.Count;

            if (playerCount == 0)
            {
                UUDebug.LogWarning("没有玩家可以分配角色");
                return;
            }

            // 计算角色数量
            int duckCount = Mathf.Clamp(Mathf.RoundToInt(playerCount * duckRatio), minDucks, maxDucks);
            int neutralCount = playerCount >= 7 ? Mathf.RoundToInt(playerCount * neutralRatio) : 0;
            int gooseCount = playerCount - duckCount - neutralCount;

            // 确保至少有一个鹅
            if (gooseCount < 1 && playerCount > 1)
            {
                gooseCount = 1;
                if (neutralCount > 0) neutralCount--;
                else duckCount--;
            }

            UDebug.Log($"角色分配: 总玩家={playerCount}, 鹅={gooseCount}, 鸭子={duckCount}, 中立={neutralCount}");

            // 洗牌玩家列表
            ShuffleList(players);

            // 分配角色
            int index = 0;

            // 分配鸭子
            for (int i = 0; i < duckCount && index < playerCount; i++)
            {
                AssignRoleToPlayer(players[index++], RoleType.Duck);
            }

            // 分配中立
            for (int i = 0; i < neutralCount && index < playerCount; i++)
            {
                AssignRoleToPlayer(players[index++], RoleType.Neutral);
            }

            // 分配鹅
            while (index < playerCount)
            {
                AssignRoleToPlayer(players[index++], RoleType.Goose);
            }

            // 通知游戏管理器角色分配完成
            UDebug.Log("角色分配完成");
        }

        /// <summary>
        /// 为指定玩家分配角色
        /// </summary>
        private void AssignRoleToPlayer(PlayerRef player, RoleType roleType)
        {
            // 创建角色数据
            var roleData = new PlayerRoleData(roleType);

            // 保存到字典
            _playerRoleData[player] = roleData;

            // 如果是鸭子，设置玩家控制器属性
            if (roleType == RoleType.Duck)
            {
                SetPlayerAsDuck(player);
            }

            // 触发事件
            OnRoleAssigned?.Invoke(player, roleType);

            UDebug.Log($"玩家 {player} 被分配为 {roleType}");
        }

        /// <summary>
        /// 设置玩家为鸭子
        /// </summary>
        private void SetPlayerAsDuck(PlayerRef player)
        {
            // 获取玩家对象
            var playerObj = Runner.GetPlayerObject(player);
            if (playerObj != null)
            {
                // 获取玩家控制器接口
                var controller = playerObj.GetComponent<IPlayerController>();
                if (controller != null)
                {
                    // 设置为杀手
                    controller.SetAsImpostor(true);
                }
            }
        }

        /// <summary>
        /// 获取所有玩家列表
        /// </summary>
        private List<PlayerRef> GetAllPlayers()
        {
            var players = new List<PlayerRef>();

            // 使用自定义网络框架的ActivePlayers属性
            var activePlayers = Runner.ActivePlayers;
            foreach (var player in activePlayers)
            {
                players.Add(player);
            }

            return players;
        }

        /// <summary>
        /// 随机打乱列表
        /// </summary>
        private void ShuffleList<T>(List<T> list)
        {
            int n = list.Count;

            while (n > 1)
            {
                n--;
                int k = UnityEngine.Random.Range(0, n + 1);
                T temp = list[k];
                list[k] = list[n];
                list[n] = temp;
            }
        }

        #endregion

        #region 角色查询

        /// <summary>
        /// 获取玩家角色类型
        /// </summary>
        public RoleType GetPlayerRole(PlayerRef player)
        {
            if (_playerRoleData.TryGetValue(player, out var data))
            {
                return data.roleType;
            }

            // 默认为鹅
            return RoleType.Goose;
        }

        /// <summary>
        /// 获取指定角色的所有玩家
        /// </summary>
        public List<PlayerRef> GetPlayersByRole(RoleType role)
        {
            var players = new List<PlayerRef>();

            foreach (var kvp in _playerRoleData)
            {
                if (kvp.Value.roleType == role)
                {
                    players.Add(kvp.Key);
                }
            }

            return players;
        }

        /// <summary>
        /// 检查玩家是否为指定角色
        /// </summary>
        public bool IsPlayerRole(PlayerRef player, RoleType role)
        {
            return GetPlayerRole(player) == role;
        }

        /// <summary>
        /// 获取指定角色的存活玩家数量
        /// </summary>
        public int GetAlivePlayerCountByRole(RoleType role)
        {
            int count = 0;

            foreach (var kvp in _playerRoleData)
            {
                if (kvp.Value.roleType == role && kvp.Value.isAlive)
                {
                    count++;
                }
            }

            return count;
        }

        /// <summary>
        /// 获取指定角色的总玩家数量
        /// </summary>
        public int GetTotalPlayerCountByRole(RoleType role)
        {
            int count = 0;

            foreach (var kvp in _playerRoleData)
            {
                if (kvp.Value.roleType == role)
                {
                    count++;
                }
            }

            return count;
        }

        /// <summary>
        /// 设置玩家死亡状态
        /// </summary>
        public void SetPlayerDead(PlayerRef player)
        {
            if (!Runner.IsServer) return;

            if (_playerRoleData.TryGetValue(player, out var data))
            {
                data.isAlive = false;

                // 触发事件
                OnPlayerDied?.Invoke(player);

                UDebug.Log($"玩家 {player} 已死亡");
            }
        }

        /// <summary>
        /// 检查玩家是否存活
        /// </summary>
        public bool IsPlayerAlive(PlayerRef player)
        {
            if (_playerRoleData.TryGetValue(player, out var data))
            {
                return data.isAlive;
            }

            return true; // 默认为存活
        }

        #endregion

        #region 能力管理

        /// <summary>
        /// 检查玩家是否可以使用特定能力
        /// </summary>
        public bool CanUseAbility(PlayerRef player, string abilityId)
        {
            // 检查玩家是否存在
            if (!_playerRoleData.TryGetValue(player, out var playerData))
                return false;

            // 检查玩家是否存活
            if (!playerData.isAlive)
                return false;

            // 检查能力是否属于该角色
            if (!IsAbilityAllowedForRole(playerData.roleType, abilityId))
                return false;

            // 检查冷却时间
            if (playerData.abilityCooldowns.TryGetValue(abilityId, out float cooldownEndTime))
            {
                if (Runner.SimulationTime < cooldownEndTime)
                    return false;
            }

            return true;
        }

        /// <summary>
        /// 使用能力
        /// </summary>
        public void UseAbility(PlayerRef player, string abilityId)
        {
            if (!Runner.IsServer) return;

            if (!CanUseAbility(player, abilityId))
                return;

            // 设置冷却
            var playerData = _playerRoleData[player];
            float cooldownTime = GetAbilityCooldown(abilityId);
            playerData.abilityCooldowns[abilityId] = Runner.SimulationTime + cooldownTime;

            UDebug.Log($"玩家 {player} 使用了能力 {abilityId}，冷却时间 {cooldownTime} 秒");
        }

        /// <summary>
        /// 获取能力冷却时间
        /// </summary>
        private float GetAbilityCooldown(string abilityId)
        {
            if (_abilityCooldowns.TryGetValue(abilityId, out float cooldown))
            {
                return cooldown;
            }

            return 30f; // 默认30秒
        }

        /// <summary>
        /// 检查能力是否允许给指定角色使用
        /// </summary>
        private bool IsAbilityAllowedForRole(RoleType roleType, string abilityId)
        {
            switch (abilityId)
            {
                case "kill":
                case "vent":
                case "sabotage":
                    return roleType == RoleType.Duck;

                case "scan":
                case "shield":
                    return roleType == RoleType.Goose;

                default:
                    return false;
            }
        }

        /// <summary>
        /// 获取能力冷却剩余时间
        /// </summary>
        public float GetAbilityCooldownRemaining(PlayerRef player, string abilityId)
        {
            if (!_playerRoleData.TryGetValue(player, out var playerData))
                return 0f;

            if (playerData.abilityCooldowns.TryGetValue(abilityId, out float cooldownEndTime))
            {
                float remaining = cooldownEndTime - Runner.SimulationTime;
                return Mathf.Max(0f, remaining);
            }

            return 0f;
        }

        #endregion

        #region 网络通信

        /// <summary>
        /// 通知客户端角色分配 - 暂时通过事件系统实现
        /// TODO: 实现自定义RPC系统后替换
        /// </summary>
        private void NotifyRoleAssigned(PlayerRef player, RoleType roleType)
        {
            // 客户端处理角色分配
            if (player == Runner.LocalPlayer)
            {
                UDebug.Log($"[网络] 您的角色是: {roleType}");

                // 这里可以更新UI或触发动画
            }
        }

        /// <summary>
        /// 通知客户端玩家死亡 - 暂时通过事件系统实现
        /// TODO: 实现自定义RPC系统后替换
        /// </summary>
        private void NotifyPlayerDied(PlayerRef player)
        {
            UDebug.Log($"[网络] 玩家 {player} 已死亡");

            // 这里可以更新UI或触发动画
        }

        #endregion
    }
}
